# python run_vision_analysis_optimized.py \
#   --prompt_path "./人体描述规划/2-1/prompt.v2.zh.md" \
#   --large_image_dir "./筛选小图" \
#   --data_dir "./裁剪后小图_带附属物" \
#   --output_dir "./analysis_results_v3" \
#   --api_key "sk-YOUR_API_KEY_HERE" \
#   --model "gpt-4-vision-preview" \
#   --max_workers 8  # 根据你的网络和需求调整并发数


"""
Optimized Vision Analysis Script
Processes cases by sending one large image and all associated small images
in a single, efficient API call. Supports concurrent processing of cases.
"""
import os
import json
import base64
import argparse
import logging
from pathlib import Path
from openai import OpenAI
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

# --- 基本配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(threadName)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
    # 支持的图片格式
image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.webp', '.tiff'}

def encode_image_to_base64(image_path: Path) -> str:
    """读取图片文件并将其编码为base64字符串。"""
    try:
        with image_path.open("rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except IOError as e:
        logging.error(f"无法读取或编码图片 {image_path}: {e}")
        raise

def get_prompt(prompt_path: Path) -> str:
    """从文件加载Prompt文本。"""
    try:
        return prompt_path.read_text(encoding='utf-8')
    except FileNotFoundError:
        logging.error(f"Prompt模板文件未找到: {prompt_path}")
        raise
    except Exception as e:
        logging.error(f"读取Prompt模板时发生错误 {prompt_path}: {e}")
        raise

def call_openai_vision_api(
    client: OpenAI,
    model: str,
    prompt: str,
    base64_large_image: str,
    base64_small_images: List[str]
) -> Dict[str, Any]:
    """
    调用OpenAI Vision API并返回JSON格式的响应。
    此次调用会发送一张大图（上下文）和多张小图（焦点）。
    """
    logging.info(f"正在调用视觉API (1张大图 + {len(base64_small_images)}张小图)...")

    # 构建消息内容
    content = [{"type": "text", "text": prompt}]
    
    # 添加大图（上下文）
    content.append({
        "type": "image_url",
        "image_url": {
            "url": f"data:image/jpeg;base64,{base64_large_image}",
            # "detail": "high" # 建议大图也用高分辨率，以便模型看清整体环境
        },
    })

    # 添加所有小图（焦点）
    for b64_img in base64_small_images:
        content.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{b64_img}",
                # "detail": "high" # 焦点图必须用高分辨率
            },
        })

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": content}],
            # max_tokens=4096, # 建议设置max_tokens以防意外
            temperature=0.0,
            # response_format={"type": "json_object"}, # 请求JSON输出，更稳定
        )
        response_text = response.choices[0].message.content
        return json.loads(response_text)

    except json.JSONDecodeError as e:
        logging.error(f"API返回的不是有效的JSON格式: {e}")
        logging.error(f"原始响应: {response_text}")
        return {"error": "Invalid JSON response", "raw_response": response_text}
    except Exception as e:
        # 捕获OpenAI的API错误
        logging.error(f"调用API时发生错误: {e}")
        return {"error": str(e)}


def process_case(
    case_dir: Path,
    large_image_dir: Path,
    output_dir: Path,
    client: OpenAI,
    model: str,
    prompt_template: str,
    dry_run: bool = False
) -> None:
    """处理单个案例（一个大图及其所有相关小图）。"""
    base_name = case_dir.name
    logging.info(f"--- 开始处理案例: {base_name} ---")

    # 1. 查找对应的大图
    large_image_path = None
    for ext in ['.jpg', '.jpeg', '.png']:
        potential_path = large_image_dir / f"{base_name}{ext}"
        if potential_path.exists():
            large_image_path = potential_path
            break
    
    if not large_image_path:
        logging.warning(f"找不到案例 '{base_name}' 的大图，跳过。")
        return

    # 2. 查找并收集所有小图
    jsonl_path = case_dir / f"{base_name}.jsonl"
    if not jsonl_path.exists():
        logging.warning(f"找不到JSONL文件 {jsonl_path}，跳过案例 {base_name}。")
        return

    small_image_paths = [] 
    # 遍历目录下所有文件
    for img_path in case_dir.glob('*'):
        if img_path.suffix.lower() in image_extensions:
            small_image_paths.append(img_path)
            logging.debug(f"找到图片: {img_path}")
    
    logging.info(f"在 {case_dir} 下共找到 {len(small_image_paths)} 张图片")
    
    if not small_image_paths:
        logging.warning(f"案例 '{base_name}' 中没有找到任何有效的小图，跳过。")
        return

    logging.info(f"找到大图: {large_image_path.name} | 找到 {len(small_image_paths)} 张小图进行分析。")

    if dry_run:
        logging.info(f"[Dry Run] 跳过对案例 '{base_name}' 的API调用。")
        return

    # 3. 编码所有图片
    try:
        base64_large_image = encode_image_to_base64(large_image_path)
        base64_small_images = [encode_image_to_base64(p) for p in small_image_paths]
    except Exception:
        logging.error(f"处理案例 '{base_name}' 的图片编码时失败，跳过。")
        return

    # 4. 调用API（单次调用）
    analysis_result = call_openai_vision_api(
        client=client,
        model=model,
        prompt=prompt_template,
        base64_large_image=base64_large_image,
        base64_small_images=base64_small_images
    )

    # 5. 保存结果
    case_output_dir = output_dir / base_name
    case_output_dir.mkdir(parents=True, exist_ok=True)
    output_json_path = case_output_dir / "analysis_result.json"
    
    with output_json_path.open('w', encoding='utf-8') as out_f:
        json.dump(analysis_result, out_f, ensure_ascii=False, indent=2)
    
    logging.info(f"    [+] 分析结果已保存至: {output_json_path}")


def main():
    parser = argparse.ArgumentParser(
        description="【优化版】使用OpenAI Vision API对图片进行批量分析。每个案例（大图+N个小图）只进行一次API调用。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    # 路径参数
    parser.add_argument("--prompt_path", type=Path, required=True, help="Prompt模板 .md 文件路径。")
    parser.add_argument("--large_image_dir", type=Path, required=True, help="大图(原图)文件夹路径。")
    parser.add_argument("--data_dir", type=Path, required=True, help="包含(小图+JSONL)子文件夹的主数据路径。")
    parser.add_argument("--output_dir", type=Path, required=True, help="保存分析结果的根文件夹路径。")
    
    # API和模型参数
    parser.add_argument("--api_key", type=str, default="sk-DpnzsrnqfZTjmicf48011f2b6e1e4bBf9d22A5Ca7301505b", help="OpenAI API密钥。")
    parser.add_argument("--api_base_url", type=str, default="https://one-api.sensoro.com/v1/", help="OpenAI API基础URL。")
    parser.add_argument("--model", type=str, default="gpt-4-vision-preview", help="要使用的模型名称。")

    # 控制参数
    parser.add_argument("--max_workers", type=int, default=1, help="用于并发处理案例的最大线程数。")
    parser.add_argument("--dry_run", action="store_true", help="执行除API调用外的所有步骤，用于测试文件查找和数据准备。")

    args = parser.parse_args()

    # 关键路径检查
    if not args.api_key and not args.dry_run:
        logging.critical("致命错误: 未提供API密钥。请使用 --api_key 或设置 OPENAI_API_KEY 环境变量。")
        return
        
    for path in [args.prompt_path, args.large_image_dir, args.data_dir]:
        if not path.exists():
            logging.critical(f"致命错误: 路径 '{path}' 不存在。")
            return
            
    # 创建输出目录
    args.output_dir.mkdir(parents=True, exist_ok=True)

    try:
        # 初始化客户端
        client = OpenAI(api_key=args.api_key, base_url=args.api_base_url)
        
        # 加载Prompt
        prompt_template = get_prompt(args.prompt_path)
        
        # 识别所有待处理的案例
        case_dirs = [d for d in args.data_dir.iterdir() if d.is_dir()]
        logging.info(f"发现 {len(case_dirs)} 个案例待处理。")
        
        if args.dry_run:
            logging.warning("--- DRY RUN模式已激活，不会调用API或写入文件 ---")

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=args.max_workers) as executor:
            future_to_case = {
                executor.submit(
                    process_case, 
                    case_dir, 
                    args.large_image_dir, 
                    args.output_dir,
                    client,
                    args.model,
                    prompt_template,
                    args.dry_run
                ): case_dir.name for case_dir in case_dirs
            }

            for future in as_completed(future_to_case):
                case_name = future_to_case[future]
                try:
                    future.result()  # 获取结果或抛出异常
                    logging.info(f"案例 '{case_name}' 处理完成。")
                except Exception as exc:
                    logging.error(f"案例 '{case_name}' 在处理过程中发生严重错误: {exc}")

        logging.info("所有任务处理完成！")

    except Exception as e:
        logging.critical(f"在主流程中发生无法恢复的错误: {e}", exc_info=True)

if __name__ == "__main__":
    main()
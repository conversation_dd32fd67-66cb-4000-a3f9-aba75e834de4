# -*- coding: utf-8 -*-
import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

class Constants:
    IVMS_DEVICE_SERVICE_URI = os.getenv("IVMS_DEVICE_SERVICE_INTERNAL")
    AISE_API_WEBHOOK_URL = os.getenv("AISE_API_WEBHOOK_URL")
    AISE_SYS_ID = os.getenv("VMS_APP_ID")
    AISE_SYS_KEY = os.getenv("VMS_APP_KEY")
    AISE_SYS_SECRET = os.getenv("VMS_APP_SECRET")
    S3_PUBLIC_ACCESS_KEY = os.getenv("S3_AI_ACCESS_KEY")
    S3_PUBLIC_SECRET_KEY = os.getenv("S3_AI_SECRET_KEY")
    OSS_SECRET_KEY = os.getenv("OSS_SECRET_KEY")